        stage('Deploy to Servers') {
            steps {
                withCredentials([
                    string(credentialsId: 'INTERNAL_OSS_ACCESS_KEY_ID', variable: 'ALI_KEY_ID'),
                    string(credentialsId: 'INTERNAL_OSS_ACCESS_KEY_SECRET', variable: 'ALI_KEY_SECRET')
                ]) {
                    script {
                        def serverDetails = SERVER_IPS.split(',')
                        def deploySuccess = true
                        def ossPackageName = env.OSS_PACKAGE_NAME
    
                        serverDetails.each { serverDetail ->
                            def (serverIp, serverPort) = serverDetail.split(':')
                            echo "开始测试部署到服务器: ${serverIp}:${serverPort}"
    
                            sshagent([SSH_KEY]) {
                                def deployResult = sh(returnStatus: true, script: """
                                    ssh -p ${serverPort} -o StrictHostKeyChecking=no ${SERVER_USER}@${serverIp} '
                                    echo "[测试模式 - 仅下载OSS包并添加日志]"
                                    cd ${DEPLOY_BASE_PATH}

                                    echo "[从 OSS 下载前端包: ${ossPackageName}]"
                                    if [ ! -f ossutil64 ]; then
                                        echo "下载 ossutil64 工具..."
                                        curl -O https://gosspublic.alicdn.com/ossutil/1.7.16/ossutil64
                                        chmod +x ossutil64
                                    fi

                                    ./ossutil64 config -e ${OSS_OUTER_ENDPOINT} -i ${ALI_KEY_ID} -k ${ALI_KEY_SECRET} --output-dir=/tmp
                                    ./ossutil64 cp oss://${OSS_BUCKET_NAME}/${OSS_FOLDER}/${ossPackageName} ${ossPackageName} -f

                                    echo "[添加成功日志到现有包中]"
                                    # 创建success.log文件
                                    echo "部署成功 - 时间: \$(date)" > success.log
                                    echo "包名: ${ossPackageName}" >> success.log
                                    echo "服务器: ${serverIp}:${serverPort}" >> success.log
                                    
                                    # 将success.log添加到现有的${DEPLOY_DIR_NAME}目录中
                                    if [ -d "${DEPLOY_DIR_NAME}" ]; then
                                        cp success.log ${DEPLOY_DIR_NAME}/
                                        echo "success.log已添加到 ${DEPLOY_DIR_NAME}/ 目录"
                                    else
                                        echo "警告: ${DEPLOY_DIR_NAME} 目录不存在"
                                    fi

                                    # 清理下载的包和临时文件
                                    rm -f ${ossPackageName}
                                    rm -f success.log

                                    echo "测试完成 - OSS包已下载，success.log已添加到现有目录"
                                    echo "当前 ${DEPLOY_DIR_NAME} 目录文件数量: \$(find ${DEPLOY_DIR_NAME} -type f | wc -l 2>/dev/null || echo 0)"
                                    '
                                """)
    
                                if (deployResult != 0) {
                                    echo "测试部署到 ${serverIp}:${serverPort} 失败，退出码: ${deployResult}"
                                    deploySuccess = false
                                } else {
                                    echo "服务器 ${serverIp}:${serverPort} 测试完成"
                                }
                            }
                        }
    
                        if (!deploySuccess) {
                            currentBuild.result = 'FAILURE'
                            error('一个或多个服务器测试失败')
                        }
                    }
                }
            }
        }